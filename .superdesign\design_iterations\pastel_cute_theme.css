:root {
  --background: #fff7fa;
  --foreground: #2d2d2d;
  --card: #ffffff;
  --card-foreground: #2d2d2d;
  --popover: #fff7fa;
  --popover-foreground: #2d2d2d;
  --primary: #ffb6d5;
  --primary-foreground: #fff;
  --secondary: #aeeaff;
  --secondary-foreground: #2d2d2d;
  --muted: #f6e7ff;
  --muted-foreground: #7d7d7d;
  --accent: #fff6b2;
  --accent-foreground: #2d2d2d;
  --destructive: #ffb4b4;
  --destructive-foreground: #fff;
  --border: #ffe3f0;
  --input: #ffe3f0;
  --ring: #ffb6d5;
  --chart-1: #ffb6d5;
  --chart-2: #aeeaff;
  --chart-3: #baffc9;
  --chart-4: #fff6b2;
  --chart-5: #f6e7ff;
  --sidebar: #fff0f6;
  --sidebar-foreground: #2d2d2d;
  --sidebar-primary: #ffb6d5;
  --sidebar-primary-foreground: #fff;
  --sidebar-accent: #aeeaff;
  --sidebar-accent-foreground: #2d2d2d;
  --sidebar-border: #ffe3f0;
  --sidebar-ring: #ffb6d5;
  --font-sans: 'Poppins', 'Segoe UI', 'Arial', sans-serif;
  --font-serif: 'Architects Daughter', cursive, serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  --radius: 24px;
  --shadow-2xs: 0 1px 2px 0 rgba(255,182,213,0.08);
  --shadow-xs: 0 2px 4px 0 rgba(174,234,255,0.10);
  --shadow-sm: 0 4px 8px 0 rgba(255,246,178,0.10);
  --shadow: 0 8px 24px 0 rgba(255,182,213,0.12);
  --shadow-md: 0 12px 32px 0 rgba(174,234,255,0.14);
  --shadow-lg: 0 16px 48px 0 rgba(186,255,201,0.16);
  --shadow-xl: 0 24px 64px 0 rgba(255,246,178,0.18);
  --shadow-2xl: 0 32px 96px 0 rgba(255,182,213,0.20);
  --tracking-normal: 0.01em;
  --spacing: 1.2rem;
  --radius-sm: 16px;
  --radius-md: 20px;
  --radius-lg: 24px;
  --radius-xl: 32px;
}

body {
  font-family: var(--font-sans) !important;
  background: var(--background) !important;
  color: var(--foreground) !important;
  letter-spacing: var(--tracking-normal) !important;
}
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-serif) !important;
  color: var(--primary) !important;
  margin-bottom: 0.5em !important;
}
.card {
  background: var(--card) !important;
  color: var(--card-foreground) !important;
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow) !important;
  padding: 2rem !important;
  margin-bottom: 2rem !important;
}
.button {
  background: var(--primary) !important;
  color: var(--primary-foreground) !important;
  border-radius: var(--radius-md) !important;
  box-shadow: var(--shadow-xs) !important;
  padding: 0.75rem 1.5rem !important;
  font-size: 1.1rem !important;
  font-family: var(--font-serif) !important;
  transition: background 0.2s, box-shadow 0.2s !important;
}
.button:hover {
  background: var(--secondary) !important;
  box-shadow: var(--shadow-md) !important;
}
input, select, textarea {
  border-radius: var(--radius-sm) !important;
  border: 2px solid var(--border) !important;
  background: var(--muted) !important;
  color: var(--foreground) !important;
  font-family: var(--font-sans) !important;
  padding: 0.75rem 1rem !important;
  margin-bottom: 1rem !important;
  box-shadow: var(--shadow-2xs) !important;
}
.sidebar {
  background: var(--sidebar) !important;
  color: var(--sidebar-foreground) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-sm) !important;
  padding: 2rem 1rem !important;
}
