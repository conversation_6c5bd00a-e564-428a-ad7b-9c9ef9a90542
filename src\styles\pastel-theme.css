/* Pastel 可愛風主題樣式 */

/* Google Fonts 引入 */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&family=Architects+Daughter&display=swap');

/* CSS 變數定義 */
:root {
  /* Pastel 色彩系統 */
  --primary-pink: #ffb6d5;
  --secondary-blue: #aeeaff;
  --accent-yellow: #fff6b2;
  --mint-green: #b8f2d0;
  --lavender: #e6d7ff;
  --peach: #ffd4c4;
  
  /* 背景色系 */
  --bg-primary: #fff7fa;
  --bg-secondary: #ffffff;
  --bg-card: #ffffff;
  --bg-hover: #fef1f5;
  
  /* 文字色系 */
  --text-primary: #2d2d2d;
  --text-secondary: #666666;
  --text-muted: #999999;
  --text-white: #ffffff;
  
  /* 邊框和陰影 */
  --border-radius: 24px;
  --border-radius-lg: 32px;
  --border-radius-sm: 16px;
  --shadow-soft: 0 8px 32px rgba(255, 182, 213, 0.15);
  --shadow-card: 0 4px 20px rgba(255, 182, 213, 0.1);
  --shadow-button: 0 4px 16px rgba(255, 182, 213, 0.2);
  
  /* 字體系統 */
  --font-heading: 'Architects Daughter', cursive;
  --font-body: 'Poppins', sans-serif;
  
  /* 間距系統 */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
}

/* 全域樣式重置 */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-body);
  background: linear-gradient(135deg, var(--bg-primary) 0%, #fef9fc 100%);
  color: var(--text-primary);
  line-height: 1.6;
  font-size: 16px;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* 標題樣式 */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: 400;
  line-height: 1.3;
  margin-bottom: var(--spacing-sm);
}

h1 {
  font-size: 2.5rem;
  color: var(--primary-pink);
  text-shadow: 2px 2px 4px rgba(255, 182, 213, 0.3);
}

h2 {
  font-size: 2rem;
  color: var(--text-primary);
}

h3 {
  font-size: 1.5rem;
  color: var(--text-primary);
}

/* 卡片樣式 */
.pastel-card {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-card);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.pastel-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-soft);
  border-color: var(--primary-pink);
}

/* 按鈕樣式 */
.pastel-btn {
  font-family: var(--font-body);
  font-weight: 500;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  text-decoration: none;
  box-shadow: var(--shadow-button);
}

.pastel-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 182, 213, 0.3);
}

.pastel-btn:active {
  transform: translateY(0);
}

/* 按鈕變體 */
.pastel-btn-primary {
  background: linear-gradient(135deg, var(--primary-pink) 0%, #ff9ec7 100%);
  color: var(--text-white);
}

.pastel-btn-secondary {
  background: linear-gradient(135deg, var(--secondary-blue) 0%, #87ceeb 100%);
  color: var(--text-primary);
}

.pastel-btn-accent {
  background: linear-gradient(135deg, var(--accent-yellow) 0%, #ffe066 100%);
  color: var(--text-primary);
}

.pastel-btn-success {
  background: linear-gradient(135deg, var(--mint-green) 0%, #90ee90 100%);
  color: var(--text-primary);
}

/* 輸入框樣式 */
.pastel-input {
  font-family: var(--font-body);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  border: 2px solid #f0f0f0;
  background: var(--bg-secondary);
  font-size: 1rem;
  transition: all 0.3s ease;
  width: 100%;
}

.pastel-input:focus {
  outline: none;
  border-color: var(--primary-pink);
  box-shadow: 0 0 0 4px rgba(255, 182, 213, 0.2);
}

/* 標籤樣式 */
.pastel-tag {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.875rem;
  font-weight: 500;
  gap: 4px;
}

.pastel-tag-pink {
  background: var(--primary-pink);
  color: var(--text-white);
}

.pastel-tag-blue {
  background: var(--secondary-blue);
  color: var(--text-primary);
}

.pastel-tag-yellow {
  background: var(--accent-yellow);
  color: var(--text-primary);
}

.pastel-tag-green {
  background: var(--mint-green);
  color: var(--text-primary);
}

/* 表格樣式 */
.pastel-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-card);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-card);
}

.pastel-table th {
  background: linear-gradient(135deg, var(--primary-pink) 0%, #ff9ec7 100%);
  color: var(--text-white);
  padding: var(--spacing-md);
  text-align: left;
  font-weight: 600;
  font-size: 1.1rem;
}

.pastel-table td {
  padding: var(--spacing-md);
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.3s ease;
}

.pastel-table tr:hover td {
  background: var(--bg-hover);
}

/* 通知樣式 */
.pastel-notification {
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: 500;
}

.pastel-notification-success {
  background: var(--mint-green);
  color: var(--text-primary);
}

.pastel-notification-info {
  background: var(--secondary-blue);
  color: var(--text-primary);
}

.pastel-notification-warning {
  background: var(--accent-yellow);
  color: var(--text-primary);
}

/* 動畫效果 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.pastel-bounce {
  animation: bounce 1s ease infinite;
}

.pastel-pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* 響應式設計 */
@media (max-width: 768px) {
  :root {
    --border-radius: 20px;
    --border-radius-lg: 24px;
    --spacing-lg: 1.5rem;
  }
  
  h1 {
    font-size: 2rem;
  }
  
  .pastel-card {
    padding: var(--spacing-md);
  }
  
  .pastel-btn {
    padding: var(--spacing-sm) var(--spacing-md);
  }
}
