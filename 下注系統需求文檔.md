## 小學生「答題下注系統」技術需求文檔

（略）

---

### 14. 前端元件實作（React）與部署方式

#### 14.1 元件：下注表單（components/BetForm.jsx）
```jsx
import { useState } from 'react';

export default function BetForm({ students, onSubmit }) {
  const [bets, setBets] = useState([]);
  const [amountLeft, setAmountLeft] = useState(100);

  const handleChange = (targetId, amount) => {
    const updated = bets.filter(b => b.targetId !== targetId);
    updated.push({ targetId, amount: Number(amount) });
    const total = updated.reduce((sum, b) => sum + b.amount, 0);
    if (total <= 100) {
      setBets(updated);
      setAmountLeft(100 - total);
    }
  };

  const handleSubmit = () => {
    if (amountLeft !== 0) return alert('請分配完整的100虛擬幣');
    onSubmit(bets);
  };

  return (
    <div className="p-4 bg-white dark:bg-gray-800 rounded-xl shadow">
      <h2 className="text-xl font-bold mb-2 text-gray-800 dark:text-white">下注區</h2>
      <p className="text-sm text-gray-500 mb-3">剩餘可分配：{amountLeft} 金幣</p>
      {students.map(s => (
        <div key={s.id} className="flex items-center gap-2 mb-2">
          <label className="w-20 text-gray-700 dark:text-white">{s.name}</label>
          <input type="number" min="0" max="100"
                 onChange={e => handleChange(s.id, e.target.value)}
                 className="w-24 p-1 rounded border" />
        </div>
      ))}
      <button onClick={handleSubmit} className="mt-4 px-4 py-2 bg-blue-600 text-white rounded">提交下注</button>
    </div>
  );
}
```

#### 14.2 元件：排行榜（components/Leaderboard.jsx）
```jsx
import React from 'react';

export default function Leaderboard({ scores }) {
  return (
    <div className="p-4 bg-white dark:bg-gray-900 rounded-xl shadow">
      <h2 className="text-xl font-bold text-gray-800 dark:text-white">本週排行榜</h2>
      <table className="mt-3 w-full text-sm">
        <thead>
          <tr className="text-gray-600 dark:text-gray-300">
            <th>#</th><th>學生</th><th>分數</th>
          </tr>
        </thead>
        <tbody>
          {scores.map((s, i) => (
            <tr key={s.student_id} className="text-center text-gray-800 dark:text-gray-100">
              <td>{i + 1}</td>
              <td>{s.name}</td>
              <td>{s.score}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
```

---

#### 14.3 明暗模式設定（tailwind.config.js）
```js
module.exports = {
  darkMode: 'class',
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  theme: { extend: {} },
  plugins: [],
};
```

#### 14.4 切換主題（App.jsx）
```jsx
function toggleTheme() {
  document.documentElement.classList.toggle('dark');
}
```

---

#### 14.5 部署與測試

- 使用 `npm install` 安裝前後端依賴
- 前端開發模式：`npm run dev`（Vite）
- 後端啟動：`node app.js`
- 系統使用 SQLite 儲存資料，免安裝資料庫
- logs/ 目錄會生成操作日誌
- 記得將 `.env.exp` 複製為 `.env` 進行環境設定

---

此為完整第一版可執行開發文檔，未來可依需求擴增使用者驗證、多人班級支援、獎勵兌換等功能。

