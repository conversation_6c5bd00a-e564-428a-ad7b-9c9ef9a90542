{"name": "student-betting-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "server": "node backend/app.js", "test": "playwright test", "test:headed": "playwright test --headed", "test:ui": "playwright test --ui", "test:report": "playwright show-report"}, "dependencies": {"axios": "^1.6.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@playwright/test": "^1.54.1", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^5.0.8"}}