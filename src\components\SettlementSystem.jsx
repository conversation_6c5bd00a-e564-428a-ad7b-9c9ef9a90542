import { useState } from 'react';
import { betsAPI } from '../utils/api';
import { showExportDialog, formatSettlementData } from '../utils/exportUtils';

export default function SettlementSystem({ students, scores }) {
  const [settlementResults, setSettlementResults] = useState(null);
  const [isCalculating, setIsCalculating] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  const calculateSettlement = async () => {
    setIsCalculating(true);
    try {
      // 獲取本週下注記錄
      const betsResponse = await betsAPI.getWeekly();
      const bets = betsResponse.data;

      // 計算每位學生的收益
      const results = {};
      
      // 初始化所有學生的結果
      students.forEach(student => {
        results[student.seat_number] = {
          name: student.name,
          seatNumber: student.seat_number,
          betsPlaced: [],
          totalWinnings: 0,
          totalBets: 0
        };
      });

      // 處理每筆下注
      bets.forEach(bet => {
        const bettor = results[bet.bettor_seat_number];
        if (bettor) {
          bettor.totalBets += 1;
          
          // 檢查是否猜中（目標學生在前3名）
          const targetScore = scores.find(s => s.seat_number === bet.target_seat_number);
          const targetRank = scores.findIndex(s => s.seat_number === bet.target_seat_number) + 1;
          
          const isWin = targetRank <= 3; // 前3名算猜中
          const winnings = isWin ? 100 : 0; // 猜中得100點
          
          bettor.totalWinnings += winnings;
          bettor.betsPlaced.push({
            targetSeatNumber: bet.target_seat_number,
            targetName: bet.target_name,
            targetScore: targetScore?.score || 0,
            targetRank: targetRank,
            isWin: isWin,
            winnings: winnings
          });
        }
      });

      setSettlementResults(results);
    } catch (error) {
      alert('計算結算失敗：' + (error.response?.data?.error || error.message));
    } finally {
      setIsCalculating(false);
    }
  };

  const handleExportResults = () => {
    if (!settlementResults) return;

    const headers = ['座號', '姓名', '下注次數', '總收益', '成功率'];
    showExportDialog(settlementResults, 'settlement_results', headers, formatSettlementData);
  };

  return (
    <div className="p-4 bg-white dark:bg-gray-800 rounded-xl shadow">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-gray-800 dark:text-white">結算系統</h2>
        <div className="flex gap-2">
          <button
            onClick={calculateSettlement}
            disabled={isCalculating}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-gray-400"
          >
            {isCalculating ? '計算中...' : '開始結算'}
          </button>
          {settlementResults && (
            <>
              <button
                onClick={() => setShowDetails(!showDetails)}
                className="px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                {showDetails ? '隱藏詳情' : '顯示詳情'}
              </button>
              <button
                onClick={handleExportResults}
                className="px-3 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
              >
                匯出結果
              </button>
            </>
          )}
        </div>
      </div>

      <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/30 rounded">
        <h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">結算規則</h3>
        <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
          <li>• 每位學生選擇3位同學下注</li>
          <li>• 如果選中的同學進入前3名，就獲得100點獎勵</li>
          <li>• 可以選中多位同學，每位都有機會獲得100點</li>
          <li>• 最高可獲得300點（3位同學都進前3名）</li>
        </ul>
      </div>

      {settlementResults && (
        <div>
          {/* 結算摘要 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3 text-gray-700 dark:text-gray-300">結算摘要</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-green-50 dark:bg-green-900/30 p-3 rounded">
                <div className="text-sm text-green-600 dark:text-green-400">總參與人數</div>
                <div className="text-2xl font-bold text-green-800 dark:text-green-200">
                  {Object.values(settlementResults).filter(r => r.totalBets > 0).length}
                </div>
              </div>
              <div className="bg-blue-50 dark:bg-blue-900/30 p-3 rounded">
                <div className="text-sm text-blue-600 dark:text-blue-400">總下注次數</div>
                <div className="text-2xl font-bold text-blue-800 dark:text-blue-200">
                  {Object.values(settlementResults).reduce((sum, r) => sum + r.totalBets, 0)}
                </div>
              </div>
              <div className="bg-purple-50 dark:bg-purple-900/30 p-3 rounded">
                <div className="text-sm text-purple-600 dark:text-purple-400">總獎勵點數</div>
                <div className="text-2xl font-bold text-purple-800 dark:text-purple-200">
                  {Object.values(settlementResults).reduce((sum, r) => sum + r.totalWinnings, 0)}
                </div>
              </div>
            </div>
          </div>

          {/* 結算結果表格 */}
          <div className="max-h-64 overflow-y-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="text-gray-600 dark:text-gray-300 border-b">
                  <th className="text-left py-2">座號</th>
                  <th className="text-left py-2">姓名</th>
                  <th className="text-center py-2">下注數</th>
                  <th className="text-center py-2">獲得點數</th>
                  <th className="text-center py-2">成功率</th>
                </tr>
              </thead>
              <tbody>
                {Object.values(settlementResults)
                  .filter(result => result.totalBets > 0)
                  .sort((a, b) => b.totalWinnings - a.totalWinnings)
                  .map(result => {
                    const successRate = result.totalBets > 0 
                      ? Math.round((result.betsPlaced.filter(b => b.isWin).length / result.totalBets) * 100)
                      : 0;
                    
                    return (
                      <tr key={result.seatNumber} className="border-b dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="py-2 font-mono">{result.seatNumber}</td>
                        <td className="py-2">{result.name}</td>
                        <td className="py-2 text-center">{result.totalBets}</td>
                        <td className="py-2 text-center font-bold text-green-600 dark:text-green-400">
                          {result.totalWinnings}
                        </td>
                        <td className="py-2 text-center">
                          <span className={`px-2 py-1 rounded text-xs ${
                            successRate >= 67 ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :
                            successRate >= 33 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :
                            'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                          }`}>
                            {successRate}%
                          </span>
                        </td>
                      </tr>
                    );
                  })}
              </tbody>
            </table>
          </div>

          {/* 詳細結果 */}
          {showDetails && (
            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-3 text-gray-700 dark:text-gray-300">詳細結果</h3>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {Object.values(settlementResults)
                  .filter(result => result.totalBets > 0)
                  .map(result => (
                    <div key={result.seatNumber} className="border dark:border-gray-600 rounded p-3">
                      <div className="font-semibold mb-2">
                        座號 {result.seatNumber} - {result.name} (總獲得: {result.totalWinnings} 點)
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
                        {result.betsPlaced.map((bet, index) => (
                          <div key={index} className={`p-2 rounded text-sm ${
                            bet.isWin 
                              ? 'bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700'
                              : 'bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700'
                          }`}>
                            <div className="font-medium">
                              座號 {bet.targetSeatNumber} - {bet.targetName}
                            </div>
                            <div className="text-xs text-gray-600 dark:text-gray-400">
                              分數: {bet.targetScore} | 排名: #{bet.targetRank}
                            </div>
                            <div className={`text-xs font-bold ${
                              bet.isWin ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                            }`}>
                              {bet.isWin ? `✓ 獲得 ${bet.winnings} 點` : '✗ 未中獎'}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
