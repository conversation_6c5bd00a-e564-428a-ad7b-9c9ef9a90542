@echo off
echo 正在啟動小學生答題下注系統...

echo.
echo 1. 安裝前端依賴...
call npm install

echo.
echo 2. 安裝後端依賴...
cd backend
call npm install
cd ..

echo.
echo 3. 啟動後端伺服器...
start "後端伺服器" cmd /k "cd backend && npm start"

echo.
echo 4. 等待後端啟動...
timeout /t 3 /nobreak > nul

echo.
echo 5. 啟動前端開發伺服器...
start "前端伺服器" cmd /k "npm run dev"

echo.
echo 系統啟動完成！
echo 前端: http://localhost:3000
echo 後端: http://localhost:3001
echo.
pause
