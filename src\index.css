@tailwind base;
@tailwind components;
@tailwind utilities;

/* 引入 Pastel 主題 */
@import './styles/pastel-theme.css';

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: 16px; /* 增加基礎字體大小 */
  line-height: 1.6; /* 增加行高提升可讀性 */
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* 加大常用元素的字體 */
.text-sm {
  font-size: 0.95rem !important;
}

.text-base {
  font-size: 1.1rem !important;
}

.text-lg {
  font-size: 1.25rem !important;
}

.text-xl {
  font-size: 1.5rem !important;
}

.text-2xl {
  font-size: 2rem !important;
}

.text-3xl {
  font-size: 2.5rem !important;
}

/* 表格字體加大 */
table {
  font-size: 1rem !important;
}

table th {
  font-size: 1.1rem !important;
  font-weight: 600 !important;
}

table td {
  font-size: 1rem !important;
  padding: 0.75rem 0.5rem !important;
}

/* 按鈕字體加大 */
button {
  font-size: 1rem !important;
  padding: 0.75rem 1rem !important;
}

/* 輸入框字體加大 */
input, textarea, select {
  font-size: 1rem !important;
  padding: 0.75rem !important;
}

/* 標籤字體加大 */
label {
  font-size: 1rem !important;
}
