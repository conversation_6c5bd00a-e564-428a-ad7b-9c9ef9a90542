// 可愛圖示元件庫
import React from 'react';

// 浮動的星星動畫
export const FloatingStars = () => {
  return (
    <div className="fixed inset-0 pointer-events-none overflow-hidden">
      {[...Array(6)].map((_, i) => (
        <div
          key={i}
          className="absolute text-yellow-300 opacity-60"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            animation: `float ${3 + Math.random() * 2}s ease-in-out infinite`,
            animationDelay: `${Math.random() * 2}s`,
            fontSize: `${1 + Math.random() * 0.5}rem`
          }}
        >
          ⭐
        </div>
      ))}
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }
      `}</style>
    </div>
  );
};

// 可愛的載入動畫
export const CuteLoader = () => {
  return (
    <div className="flex items-center justify-center py-8">
      <div className="flex space-x-2">
        {['🌸', '🌺', '🌻'].map((emoji, i) => (
          <div
            key={i}
            className="text-2xl"
            style={{
              animation: `bounce 1.4s ease-in-out ${i * 0.2}s infinite`
            }}
          >
            {emoji}
          </div>
        ))}
      </div>
    </div>
  );
};

// 成功提示動畫
export const SuccessAnimation = ({ message, onClose }) => {
  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-20">
      <div className="pastel-card text-center" style={{maxWidth: '400px'}}>
        <div className="text-6xl mb-4 pastel-bounce">🎉</div>
        <h3 style={{fontFamily: 'Architects Daughter, cursive', color: '#2d2d2d'}}>
          太棒了！
        </h3>
        <p style={{fontFamily: 'Poppins, sans-serif', color: '#666666', marginBottom: '1.5rem'}}>
          {message}
        </p>
        <button
          onClick={onClose}
          className="pastel-btn pastel-btn-primary"
        >
          ✨ 繼續
        </button>
      </div>
    </div>
  );
};

// 可愛的空狀態
export const CuteEmptyState = ({ icon, title, description }) => {
  return (
    <div className="text-center py-12">
      <div className="text-8xl mb-4 pastel-pulse">{icon}</div>
      <h3 style={{fontFamily: 'Architects Daughter, cursive', color: '#2d2d2d'}}>
        {title}
      </h3>
      <p style={{fontFamily: 'Poppins, sans-serif', color: '#666666', fontSize: '1.1rem'}}>
        {description}
      </p>
    </div>
  );
};

// 可愛的統計卡片
export const CuteStatCard = ({ icon, title, value, color = '#ffb6d5' }) => {
  return (
    <div className="pastel-card hover:scale-105 transition-transform duration-300">
      <div className="flex items-center gap-4">
        <div 
          className="w-16 h-16 rounded-full flex items-center justify-center text-2xl"
          style={{background: `linear-gradient(135deg, ${color} 0%, ${color}cc 100%)`}}
        >
          {icon}
        </div>
        <div>
          <h3 style={{fontFamily: 'Architects Daughter, cursive', color: '#2d2d2d'}} className="text-lg font-bold">
            {title}
          </h3>
          <p style={{fontFamily: 'Poppins, sans-serif', fontSize: '2rem', fontWeight: '700', color: '#2d2d2d'}}>
            {value}
          </p>
        </div>
      </div>
    </div>
  );
};

// 可愛的進度條
export const CuteProgressBar = ({ progress, color = '#ffb6d5' }) => {
  return (
    <div className="w-full">
      <div 
        className="h-3 rounded-full overflow-hidden"
        style={{background: '#f0f0f0'}}
      >
        <div 
          className="h-full rounded-full transition-all duration-500 ease-out"
          style={{
            width: `${progress}%`,
            background: `linear-gradient(135deg, ${color} 0%, ${color}cc 100%)`
          }}
        />
      </div>
      <div className="flex justify-between mt-2">
        <span style={{fontFamily: 'Poppins, sans-serif', fontSize: '0.875rem', color: '#666666'}}>
          進度
        </span>
        <span style={{fontFamily: 'Poppins, sans-serif', fontSize: '0.875rem', fontWeight: '600', color: '#2d2d2d'}}>
          {progress}%
        </span>
      </div>
    </div>
  );
};

// 可愛的提示氣泡
export const CuteTooltip = ({ children, text, position = 'top' }) => {
  return (
    <div className="relative group">
      {children}
      <div className={`
        absolute z-10 px-3 py-2 text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300
        ${position === 'top' ? 'bottom-full mb-2' : 'top-full mt-2'}
        left-1/2 transform -translate-x-1/2
      `}
      style={{
        background: '#2d2d2d',
        color: '#ffffff',
        fontFamily: 'Poppins, sans-serif',
        whiteSpace: 'nowrap'
      }}>
        {text}
        <div className={`
          absolute w-2 h-2 transform rotate-45
          ${position === 'top' ? 'top-full -mt-1' : 'bottom-full -mb-1'}
          left-1/2 -translate-x-1/2
        `}
        style={{background: '#2d2d2d'}} />
      </div>
    </div>
  );
};

// 可愛的按鈕組
export const CuteButtonGroup = ({ buttons, activeButton, onButtonClick }) => {
  return (
    <div className="flex gap-2 p-2 rounded-2xl" style={{background: '#fff7fa'}}>
      {buttons.map((button, index) => (
        <button
          key={index}
          onClick={() => onButtonClick(button.id)}
          className={`
            px-4 py-2 rounded-xl transition-all duration-300 font-medium
            ${activeButton === button.id ? 'transform scale-105' : 'hover:scale-102'}
          `}
          style={{
            background: activeButton === button.id 
              ? 'linear-gradient(135deg, #ffb6d5 0%, #ff9ec7 100%)'
              : 'transparent',
            color: activeButton === button.id ? '#ffffff' : '#666666',
            fontFamily: 'Poppins, sans-serif',
            boxShadow: activeButton === button.id ? '0 4px 16px rgba(255, 182, 213, 0.3)' : 'none'
          }}
        >
          <span className="mr-2">{button.icon}</span>
          {button.label}
        </button>
      ))}
    </div>
  );
};

// 可愛的分隔線
export const CuteDivider = ({ text }) => {
  return (
    <div className="flex items-center my-6">
      <div className="flex-1 h-px" style={{background: 'linear-gradient(to right, transparent, #ffb6d5, transparent)'}} />
      {text && (
        <span 
          className="px-4 text-sm font-medium"
          style={{fontFamily: 'Poppins, sans-serif', color: '#666666'}}
        >
          {text}
        </span>
      )}
      <div className="flex-1 h-px" style={{background: 'linear-gradient(to left, transparent, #ffb6d5, transparent)'}} />
    </div>
  );
};
