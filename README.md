# 學生答題投資系統

一個有趣的教育遊戲系統，讓學生可以對同學的答題表現進行虛擬投資，增加學習的互動性和趣味性。答越多賺越多！

## 功能特色

- 🎯 **投資系統**: 學生可以選擇3位同學進行投資
- 📊 **即時排行榜**: 顯示本週學生分數排名
- 🌙 **明暗模式**: 支援明暗主題切換
- 📱 **響應式設計**: 適配各種螢幕尺寸
- 💾 **SQLite資料庫**: 輕量級資料儲存，無需額外安裝

## 技術架構

### 前端
- **React 18** - 現代化UI框架
- **Vite** - 快速開發建置工具
- **Tailwind CSS** - 實用優先的CSS框架
- **Axios** - HTTP客戶端

### 後端
- **Node.js** - JavaScript運行環境
- **Express.js** - Web應用框架
- **SQLite3** - 輕量級資料庫
- **CORS** - 跨域資源共享

## 快速開始

### 方法一：使用啟動腳本（推薦）

1. 雙擊 `start.bat` 檔案
2. 等待自動安裝依賴和啟動服務
3. 開啟瀏覽器訪問 http://localhost:3000

### 方法二：手動啟動

1. **安裝依賴**
   ```bash
   # 安裝前端依賴
   npm install
   
   # 安裝後端依賴
   cd backend
   npm install
   cd ..
   ```

2. **啟動後端服務**
   ```bash
   cd backend
   npm start
   ```

3. **啟動前端服務**（新開終端）
   ```bash
   npm run dev
   ```

4. **訪問應用**
   - 前端: http://localhost:3000
   - 後端API: http://localhost:3001

## 專案結構

```
投資同學系統/
├── src/                    # 前端源碼
│   ├── components/         # React元件
│   │   ├── BetForm.jsx    # 下注表單
│   │   └── Leaderboard.jsx # 排行榜
│   ├── utils/             # 工具函數
│   │   └── api.js         # API呼叫
│   ├── App.jsx            # 主應用元件
│   ├── main.jsx           # 應用入口
│   └── index.css          # 全域樣式
├── backend/               # 後端源碼
│   ├── routes/            # API路由
│   │   ├── students.js    # 學生管理
│   │   ├── bets.js        # 下注功能
│   │   └── scores.js      # 分數管理
│   ├── models/            # 資料模型
│   │   └── database.js    # 資料庫操作
│   ├── app.js             # 後端主程式
│   └── package.json       # 後端依賴
├── public/                # 靜態資源
├── logs/                  # 日誌檔案
├── database.sqlite        # SQLite資料庫
├── package.json           # 前端依賴
├── vite.config.js         # Vite設定
├── tailwind.config.js     # Tailwind設定
├── start.bat              # 啟動腳本
└── README.md              # 專案說明
```

## API端點

### 學生管理
- `GET /api/students` - 獲取所有學生
- `GET /api/students/:id` - 獲取特定學生
- `POST /api/students` - 新增學生

### 下注功能
- `POST /api/bets` - 提交下注
- `GET /api/bets/week/:week` - 獲取週次下注記錄
- `POST /api/bets/calculate-returns` - 計算收益

### 分數管理
- `GET /api/scores` - 獲取本週排行榜
- `GET /api/scores/week/:week` - 獲取特定週次排行榜
- `PUT /api/scores/:studentId` - 更新學生分數
- `POST /api/scores/random` - 生成隨機分數（測試用）

## 使用說明

1. **查看排行榜**: 系統會顯示本週所有學生的分數排名
2. **進行下注**: 在下注區為不同學生分配虛擬幣（總計100幣）
3. **提交下注**: 確認分配後點擊提交按鈕
4. **切換主題**: 點擊右上角的月亮/太陽圖示切換明暗模式

## 開發說明

### 環境變數
複製 `.env.example` 為 `.env` 並根據需要調整設定：

```env
PORT=3001
DATABASE_PATH=./database.sqlite
NODE_ENV=development
LOG_LEVEL=info
LOG_DIR=./logs
```

### 資料庫結構
系統使用SQLite資料庫，包含以下表格：
- `students` - 學生資料
- `bets` - 下注記錄
- `scores` - 分數記錄

### 測試功能
- 訪問 `/api/scores/random` 可生成隨機分數用於測試
- 訪問 `/api/health` 可檢查後端服務狀態

## 未來擴展

- 🔐 使用者驗證系統
- 👥 多班級支援
- 🎁 獎勵兌換功能
- 📈 歷史數據分析
- 📧 通知系統

## 授權

此專案僅供教育用途使用。
