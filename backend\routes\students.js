import express from 'express';

const router = express.Router();

// 獲取所有學生
router.get('/', async (req, res) => {
  try {
    const students = await req.db.query('SELECT * FROM students ORDER BY seat_number');
    res.json(students);
  } catch (error) {
    console.error('獲取學生資料失敗:', error);
    res.status(500).json({ error: '獲取學生資料失敗' });
  }
});

// 獲取特定學生（依座號）
router.get('/seat/:seatNumber', async (req, res) => {
  try {
    const { seatNumber } = req.params;
    const students = await req.db.query('SELECT * FROM students WHERE seat_number = ?', [seatNumber]);

    if (students.length === 0) {
      return res.status(404).json({ error: '學生不存在' });
    }

    res.json(students[0]);
  } catch (error) {
    console.error('獲取學生資料失敗:', error);
    res.status(500).json({ error: '獲取學生資料失敗' });
  }
});

// 獲取特定學生（依ID）
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const students = await req.db.query('SELECT * FROM students WHERE id = ?', [id]);

    if (students.length === 0) {
      return res.status(404).json({ error: '學生不存在' });
    }

    res.json(students[0]);
  } catch (error) {
    console.error('獲取學生資料失敗:', error);
    res.status(500).json({ error: '獲取學生資料失敗' });
  }
});

// 新增學生
router.post('/', async (req, res) => {
  try {
    const { name, seatNumber } = req.body;

    if (!name || !seatNumber) {
      return res.status(400).json({ error: '學生姓名和座號為必填' });
    }

    const result = await req.db.run('INSERT INTO students (seat_number, name) VALUES (?, ?)', [seatNumber, name]);

    // 為新學生初始化本週分數
    const currentWeek = req.db.getCurrentWeek();
    await req.db.run(
      'INSERT INTO scores (seat_number, score, week) VALUES (?, ?, ?)',
      [seatNumber, 0, currentWeek]
    );

    res.status(201).json({ id: result.id, seatNumber, name, message: '學生新增成功' });
  } catch (error) {
    console.error('新增學生失敗:', error);
    res.status(500).json({ error: '新增學生失敗' });
  }
});

// 更新學生資料
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, seatNumber } = req.body;

    if (!name || !seatNumber) {
      return res.status(400).json({ error: '學生姓名和座號為必填' });
    }

    await req.db.run(
      'UPDATE students SET name = ?, seat_number = ? WHERE id = ?',
      [name, seatNumber, id]
    );

    res.json({ message: '學生資料更新成功', id, name, seatNumber });
  } catch (error) {
    console.error('更新學生失敗:', error);
    res.status(500).json({ error: '更新學生失敗' });
  }
});

// 刪除學生
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 檢查學生是否存在
    const student = await req.db.query('SELECT * FROM students WHERE id = ?', [id]);
    if (student.length === 0) {
      return res.status(404).json({ error: '學生不存在' });
    }

    // 刪除相關的下注和分數記錄
    await req.db.run('DELETE FROM bets WHERE bettor_seat_number = ? OR target_seat_number = ?',
      [student[0].seat_number, student[0].seat_number]);
    await req.db.run('DELETE FROM scores WHERE seat_number = ?', [student[0].seat_number]);
    await req.db.run('DELETE FROM students WHERE id = ?', [id]);

    res.json({ message: '學生刪除成功', id });
  } catch (error) {
    console.error('刪除學生失敗:', error);
    res.status(500).json({ error: '刪除學生失敗' });
  }
});

// 批量新增學生
router.post('/batch', async (req, res) => {
  try {
    const { students } = req.body;

    if (!students || !Array.isArray(students)) {
      return res.status(400).json({ error: '學生資料格式錯誤' });
    }

    const currentWeek = req.db.getCurrentWeek();
    let addedCount = 0;

    for (const studentData of students) {
      const { seatNumber, name } = studentData;

      if (!name || !seatNumber) {
        continue; // 跳過無效資料
      }

      try {
        const result = await req.db.run(
          'INSERT INTO students (seat_number, name) VALUES (?, ?)',
          [seatNumber, name]
        );

        // 為新學生初始化本週分數
        await req.db.run(
          'INSERT INTO scores (seat_number, score, week) VALUES (?, ?, ?)',
          [seatNumber, 0, currentWeek]
        );

        addedCount++;
      } catch (error) {
        console.error(`新增學生失敗 (座號${seatNumber}):`, error);
        // 繼續處理其他學生
      }
    }

    res.json({ message: `批量新增完成，成功新增 ${addedCount} 位學生` });
  } catch (error) {
    console.error('批量新增學生失敗:', error);
    res.status(500).json({ error: '批量新增學生失敗' });
  }
});

export default router;
