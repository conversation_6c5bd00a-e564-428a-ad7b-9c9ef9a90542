import axios from 'axios';

// 設定基礎URL
const API_BASE_URL = import.meta.env.VITE_API_URL || '/api';

// 建立axios實例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 請求攔截器
api.interceptors.request.use(
  (config) => {
    console.log('API請求:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    console.error('API請求錯誤:', error);
    return Promise.reject(error);
  }
);

// 回應攔截器
api.interceptors.response.use(
  (response) => {
    console.log('API回應:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('API回應錯誤:', error.response?.status, error.response?.data);
    return Promise.reject(error);
  }
);

// API方法
export const studentsAPI = {
  // 獲取所有學生
  getAll: () => api.get('/students'),

  // 獲取特定學生
  getById: (id) => api.get(`/students/${id}`),

  // 獲取特定學生（依座號）
  getBySeat: (seatNumber) => api.get(`/students/seat/${seatNumber}`),

  // 新增學生
  create: (name, seatNumber) => api.post('/students', { name, seatNumber }),

  // 更新學生
  update: (id, name, seatNumber) => api.put(`/students/${id}`, { name, seatNumber }),

  // 刪除學生
  delete: (id) => api.delete(`/students/${id}`),

  // 批量新增學生
  batchCreate: (students) => api.post('/students/batch', { students }),
};

export const betsAPI = {
  // 提交下注
  submit: (bets, bettorSeatNumber) => api.post('/bets', { bets, bettorSeatNumber }),

  // 獲取本週下注記錄
  getWeekly: (week) => api.get(`/bets/week/${week || ''}`),

  // 計算收益
  calculateReturns: (week) => api.post('/bets/calculate-returns', { week }),

  // 獲取特定學生被下注的記錄
  getBetsOnStudent: (seatNumber, week) => api.get(`/bets/on-student/${seatNumber}${week ? `?week=${week}` : ''}`),

  // 獲取下注統計
  getStats: (week) => api.get(`/bets/stats/${week || ''}`),
};

export const scoresAPI = {
  // 獲取本週排行榜
  getCurrent: () => api.get('/scores'),

  // 獲取特定週次排行榜
  getByWeek: (week) => api.get(`/scores/week/${week}`),

  // 更新學生分數（依座號）
  updateStudentBySeat: (seatNumber, score, week) =>
    api.put(`/scores/seat/${seatNumber}`, { score, week }),

  // 批量更新分數
  updateBatch: (scores, week) =>
    api.put('/scores', { scores, week }),

  // 生成隨機分數（測試用）
  generateRandom: () => api.post('/scores/random'),
};

export const healthAPI = {
  // 健康檢查
  check: () => api.get('/health'),
};

export default api;
