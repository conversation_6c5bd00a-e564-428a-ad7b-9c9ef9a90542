import { test, expect } from '@playwright/test';

test.describe('投資同學系統 - 基本功能測試', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    // 等待頁面完全加載
    await expect(page.locator('h1')).toBeVisible();
  });

  test('應用程式能正常加載首頁', async ({ page }) => {
    // 檢查頁面標題
    await expect(page).toHaveTitle(/投資同學系統|Student Betting System/);
    
    // 檢查主要元素是否存在
    await expect(page.locator('text=投資同學系統')).toBeVisible();
    await expect(page.locator('text=學生答題投資系統')).toBeVisible();
    
    // 檢查管理員按鈕
    await expect(page.locator('text=管理員')).toBeVisible();
  });

  test('可以切換主題模式', async ({ page }) => {
    // 點擊主題切換按鈕
    const themeButton = page.locator('button:has-text("暗色"), button:has-text("亮色")');
    await expect(themeButton).toBeVisible();
    await themeButton.click();
    
    // 檢查主題是否改變
    await expect(themeButton).toBeVisible();
  });

  test('可以切換到管理員面板', async ({ page }) => {
    // 點擊管理員按鈕
    await page.click('text=管理員');
    
    // 檢查管理員面板是否顯示
    await expect(page.locator('text=管理員面板')).toBeVisible({ timeout: 10000 });
    await expect(page.locator('text=系統管理與數據維護')).toBeVisible();
  });
});