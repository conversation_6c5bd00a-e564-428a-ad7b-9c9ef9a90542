# 教師用電腦部署與啟動說明

## 1. 安裝 Node.js
請先到 [Node.js 官方網站](https://nodejs.org/) 下載並安裝 LTS 版本。

## 2. 下載專案原始碼
將整個專案資料夾（包含 backend、src、public 等）複製到新電腦。

## 3. 安裝相依套件
打開 PowerShell 或命令提示字元，依序執行：

```
# 在專案根目錄
npm install

# 進入 backend 資料夾
cd backend
npm install
```

## 4. 準備資料庫
- 若有現成的 `database.sqlite`，直接複製到專案根目錄。
- 若需全新資料庫，啟動後端時會自動建立。

## 5. 一鍵啟動
專案根目錄有 `start-all.bat`，雙擊即可同時啟動前端與後端。

## 6. 手動啟動（如需）
- 後端：
  ```
  cd backend
  node app.js
  ```
- 前端：
  ```
  cd <專案根目錄>
  npm run dev
  ```

## 7. 使用說明
- 前端網址：http://localhost:5173
- 後端 API：http://localhost:3000

## 8. 常見問題
- 若遇到權限或防火牆問題，請允許相關連線。
- 如有套件安裝錯誤，請確認 Node.js 版本與網路連線。

---
如有其他需求，請聯絡系統管理員。
