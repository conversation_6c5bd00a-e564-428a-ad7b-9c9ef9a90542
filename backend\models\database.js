import sqlite3 from 'sqlite3';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class Database {
  constructor() {
    this.db = null;
  }

  async init() {
    return new Promise((resolve, reject) => {
      const dbPath = join(__dirname, '../../database.sqlite');
      this.db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
          console.error('資料庫連接失敗:', err);
          reject(err);
        } else {
          console.log('資料庫連接成功');
          this.createTables().then(resolve).catch(reject);
        }
      });
    });
  }

  async createTables() {
    return new Promise((resolve, reject) => {
      const queries = [
        `CREATE TABLE IF NOT EXISTS students (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          seat_number INTEGER NOT NULL UNIQUE,
          name TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`,
        `CREATE TABLE IF NOT EXISTS bets (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          bettor_seat_number INTEGER NOT NULL,
          target_seat_number INTEGER NOT NULL,
          amount INTEGER NOT NULL,
          week INTEGER NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (bettor_seat_number) REFERENCES students(seat_number),
          FOREIGN KEY (target_seat_number) REFERENCES students(seat_number)
        )`,
        `CREATE TABLE IF NOT EXISTS scores (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          seat_number INTEGER NOT NULL,
          score INTEGER NOT NULL DEFAULT 0,
          week INTEGER NOT NULL,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (seat_number) REFERENCES students(seat_number),
          UNIQUE(seat_number, week)
        )`
      ];

      let completed = 0;
      queries.forEach((query, index) => {
        this.db.run(query, (err) => {
          if (err) {
            console.error(`建立表格失敗 (${index}):`, err);
            reject(err);
          } else {
            completed++;
            if (completed === queries.length) {
              console.log('所有表格建立完成');
              this.insertSampleData().then(resolve).catch(reject);
            }
          }
        });
      });
    });
  }

  async insertSampleData() {
    return new Promise((resolve, reject) => {
      // 檢查是否已有學生資料
      this.db.get('SELECT COUNT(*) as count FROM students', (err, row) => {
        if (err) {
          reject(err);
          return;
        }

        if (row.count === 0) {
          // 插入28位學生資料
          const students = [
            '王小明', '李小華', '張小美', '陳小強', '林小芳',
            '黃小龍', '吳小鳳', '劉小傑', '蔡小雯', '楊小宇',
            '許小婷', '鄭小豪', '謝小琪', '洪小偉', '孫小慧',
            '施小峰', '朱小玲', '高小勇', '羅小萍', '梁小軒',
            '潘小怡', '曾小凱', '范小雅', '董小翔', '石小蓉',
            '田小浩', '余小晴', '魏小安'
          ];
          let inserted = 0;

          students.forEach((name, index) => {
            const seatNumber = index + 1; // 座號從1開始
            this.db.run('INSERT INTO students (seat_number, name) VALUES (?, ?)', [seatNumber, name], (err) => {
              if (err) {
                console.error('插入學生資料失敗:', err);
                reject(err);
              } else {
                inserted++;
                if (inserted === students.length) {
                  console.log('28位學生資料插入完成');
                  this.initializeScores().then(resolve).catch(reject);
                }
              }
            });
          });
        } else {
          resolve();
        }
      });
    });
  }

  async initializeScores() {
    return new Promise((resolve, reject) => {
      const currentWeek = this.getCurrentWeek();
      
      // 為所有學生初始化本週分數
      this.db.all('SELECT seat_number FROM students', (err, students) => {
        if (err) {
          reject(err);
          return;
        }

        let initialized = 0;
        students.forEach(student => {
          this.db.run(
            'INSERT OR IGNORE INTO scores (seat_number, score, week) VALUES (?, ?, ?)',
            [student.seat_number, Math.floor(Math.random() * 100), currentWeek],
            (err) => {
              if (err) {
                console.error('初始化分數失敗:', err);
                reject(err);
              } else {
                initialized++;
                if (initialized === students.length) {
                  console.log('學生分數初始化完成');
                  resolve();
                }
              }
            }
          );
        });
      });
    });
  }

  getCurrentWeek() {
    const now = new Date();
    const startOfYear = new Date(now.getFullYear(), 0, 1);
    const pastDaysOfYear = (now - startOfYear) / 86400000;
    return Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
  }

  // 通用查詢方法
  query(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // 通用執行方法
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }

  close() {
    if (this.db) {
      this.db.close();
    }
  }
}

export default Database;
