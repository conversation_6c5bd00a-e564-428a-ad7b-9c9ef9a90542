import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import Database from './models/database.js';
import studentsRouter from './routes/students.js';
import betsRouter from './routes/bets.js';
import scoresRouter from './routes/scores.js';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// 中間件
app.use(cors());
app.use(express.json());

// 初始化資料庫
const db = new Database();
await db.init();

// 將資料庫實例傳遞給路由
app.use((req, res, next) => {
  req.db = db;
  next();
});

// 路由
app.use('/api/students', studentsRouter);
app.use('/api/bets', betsRouter);
app.use('/api/scores', scoresRouter);

// 健康檢查
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// 錯誤處理中間件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: '伺服器內部錯誤' });
});

app.listen(PORT, () => {
  console.log(`後端伺服器運行在 http://localhost:${PORT}`);
});
