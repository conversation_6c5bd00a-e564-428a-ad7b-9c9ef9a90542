import express from 'express';

const router = express.Router();

// 提交下注
router.post('/', async (req, res) => {
  try {
    const { bets, bettorSeatNumber } = req.body;

    if (!bets || !Array.isArray(bets) || !bettorSeatNumber) {
      return res.status(400).json({ error: '下注資料格式錯誤或缺少下注者座號' });
    }

    // 驗證下注數量限制（剛好3位同學）
    if (bets.length !== 3) {
      return res.status(400).json({ error: '必須選擇剛好3位同學下注' });
    }

    // 驗證不能對自己下注
    const selfBet = bets.find(bet => bet.targetSeatNumber === bettorSeatNumber);
    if (selfBet) {
      return res.status(400).json({ error: '不能對自己下注' });
    }

    const currentWeek = getCurrentWeek();

    // 檢查本週是否已經下注
    const existingBets = await req.db.query(
      'SELECT COUNT(*) as count FROM bets WHERE bettor_seat_number = ? AND week = ?',
      [bettorSeatNumber, currentWeek]
    );

    if (existingBets[0].count > 0) {
      return res.status(400).json({ error: '本週已經下注過了' });
    }

    // 插入所有下注記錄
    for (const bet of bets) {
      await req.db.run(
        'INSERT INTO bets (bettor_seat_number, target_seat_number, amount, week) VALUES (?, ?, ?, ?)',
        [bettorSeatNumber, bet.targetSeatNumber, bet.amount, currentWeek]
      );
    }

    res.json({ message: '下注成功', week: currentWeek, betsCount: bets.length });
  } catch (error) {
    console.error('下注失敗:', error);
    res.status(500).json({ error: '下注失敗' });
  }
});

// 獲取本週下注記錄
router.get('/week/:week?', async (req, res) => {
  try {
    const week = req.params.week || getCurrentWeek();

    const bets = await req.db.query(`
      SELECT
        b.*,
        bettor.name as bettor_name,
        bettor.seat_number as bettor_seat,
        target.name as target_name,
        target.seat_number as target_seat
      FROM bets b
      JOIN students bettor ON b.bettor_seat_number = bettor.seat_number
      JOIN students target ON b.target_seat_number = target.seat_number
      WHERE b.week = ?
      ORDER BY b.created_at DESC
    `, [week]);

    res.json(bets);
  } catch (error) {
    console.error('獲取下注記錄失敗:', error);
    res.status(500).json({ error: '獲取下注記錄失敗' });
  }
});

// 計算下注收益
router.post('/calculate-returns', async (req, res) => {
  try {
    const { week = getCurrentWeek() } = req.body;

    // 獲取本週所有下注和分數
    const bets = await req.db.query(`
      SELECT b.*, s.score
      FROM bets b
      JOIN scores s ON b.target_seat_number = s.seat_number AND b.week = s.week
      WHERE b.week = ?
    `, [week]);

    // 計算每個下注者的收益
    const returns = {};

    bets.forEach(bet => {
      if (!returns[bet.bettor_seat_number]) {
        returns[bet.bettor_seat_number] = {
          totalBet: 0,
          totalReturn: 0,
          bets: []
        };
      }

      const returnAmount = Math.floor(bet.amount * (bet.score / 100));
      returns[bet.bettor_seat_number].totalBet += bet.amount;
      returns[bet.bettor_seat_number].totalReturn += returnAmount;
      returns[bet.bettor_seat_number].bets.push({
        targetSeatNumber: bet.target_seat_number,
        amount: bet.amount,
        score: bet.score,
        return: returnAmount
      });
    });

    res.json(returns);
  } catch (error) {
    console.error('計算收益失敗:', error);
    res.status(500).json({ error: '計算收益失敗' });
  }
});

// 獲取特定學生被下注的記錄
router.get('/on-student/:seatNumber', async (req, res) => {
  try {
    const { seatNumber } = req.params;
    const { week = getCurrentWeek() } = req.query;

    const bets = await req.db.query(`
      SELECT
        b.*,
        bettor.name as bettor_name,
        bettor.seat_number as bettor_seat_number
      FROM bets b
      JOIN students bettor ON b.bettor_seat_number = bettor.seat_number
      WHERE b.target_seat_number = ? AND b.week = ?
      ORDER BY b.created_at DESC
    `, [seatNumber, week]);

    res.json(bets);
  } catch (error) {
    console.error('獲取被下注記錄失敗:', error);
    res.status(500).json({ error: '獲取被下注記錄失敗' });
  }
});

// 獲取下注統計
router.get('/stats/:week?', async (req, res) => {
  try {
    const week = req.params.week || getCurrentWeek();

    // 獲取本週下注統計
    const stats = await req.db.query(`
      SELECT
        target_seat_number,
        target.name as target_name,
        COUNT(*) as bet_count
      FROM bets b
      JOIN students target ON b.target_seat_number = target.seat_number
      WHERE b.week = ?
      GROUP BY target_seat_number, target.name
      ORDER BY bet_count DESC, target_seat_number ASC
    `, [week]);

    res.json(stats);
  } catch (error) {
    console.error('獲取下注統計失敗:', error);
    res.status(500).json({ error: '獲取下注統計失敗' });
  }
});

function getCurrentWeek() {
  const now = new Date();
  const startOfYear = new Date(now.getFullYear(), 0, 1);
  const pastDaysOfYear = (now - startOfYear) / 86400000;
  return Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
}

export default router;
