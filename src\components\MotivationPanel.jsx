import { useState, useEffect } from 'react';
import { betsAPI } from '../utils/api';

export default function MotivationPanel({ selectedStudent, students }) {
  const [betsOnMe, setBetsOnMe] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (selectedStudent) {
      fetchBetsOnMe();
    }
  }, [selectedStudent]);

  const fetchBetsOnMe = async () => {
    if (!selectedStudent) return;
    
    setLoading(true);
    try {
      const response = await betsAPI.getBetsOnStudent(selectedStudent.seat_number);
      setBetsOnMe(response.data);
    } catch (error) {
      console.error('獲取下注記錄失敗:', error);
      setBetsOnMe([]);
    } finally {
      setLoading(false);
    }
  };

  if (!selectedStudent) {
    return (
      <div className="pastel-card">
        <div className="flex items-center gap-3 mb-4">
          <span className="text-3xl">💝</span>
          <h2 style={{fontFamily: 'Architects Daughter, cursive', color: '#2d2d2d'}} className="text-2xl font-bold">
            激勵面板
          </h2>
        </div>
        <div className="text-center py-8">
          <div className="text-6xl mb-4">🎯</div>
          <p style={{color: '#666666', fontFamily: 'Poppins, sans-serif', fontSize: '1.1rem'}}>
            請先選擇你的座號查看支持者
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="pastel-card">
      <div className="flex items-center gap-4 mb-6">
        <div className="w-16 h-16 rounded-full flex items-center justify-center text-white font-bold text-xl pastel-pulse"
             style={{background: 'linear-gradient(135deg, #ffb6d5 0%, #ff9ec7 100%)'}}>
          {selectedStudent.seat_number}
        </div>
        <div>
          <h2 style={{fontFamily: 'Architects Daughter, cursive', color: '#2d2d2d'}} className="text-2xl font-bold">
            {selectedStudent.name} 的激勵面板
          </h2>
          <p style={{fontFamily: 'Poppins, sans-serif', color: '#666666', fontSize: '1rem'}}>
            看看誰對你有信心！💖
          </p>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <div className="text-gray-500 dark:text-gray-400">載入中...</div>
        </div>
      ) : betsOnMe.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-6xl mb-4">😔</div>
          <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-2">
            還沒有人投資你
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            努力表現，讓同學們對你有信心吧！
          </p>
        </div>
      ) : (
        <div>
          <div className="mb-4 p-3 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/30 dark:to-blue-900/30 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-2xl">🎉</span>
              <h3 className="text-lg font-semibold text-gray-800 dark:text-white">
                太棒了！有 {betsOnMe.length} 位同學投資你
              </h3>
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              他們相信你會有好表現，加油！
            </p>
          </div>

          <div className="space-y-3">
            <h4 className="font-semibold text-gray-700 dark:text-gray-300">
              支持你的同學們：
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {betsOnMe.map((bet, index) => {
                const bettor = students.find(s => s.seat_number === bet.bettor_seat_number);
                return (
                  <div key={index} className="p-3 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/30 dark:to-orange-900/30 rounded-lg border border-yellow-200 dark:border-yellow-700">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-white font-bold">
                        {bet.bettor_seat_number}
                      </div>
                      <div className="flex-1">
                        <div className="font-medium text-gray-800 dark:text-white">
                          {bet.bettor_name || bettor?.name || '未知同學'}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          座號 {bet.bettor_seat_number}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg">⭐</div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          支持你
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/30 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-xl">💪</span>
              <h4 className="font-semibold text-blue-800 dark:text-blue-200">
                激勵小語
              </h4>
            </div>
            <p className="text-blue-700 dark:text-blue-300 text-sm">
              {getMotivationalMessage(betsOnMe.length)}
            </p>
          </div>

          <div className="mt-4 text-center">
            <button
              onClick={fetchBetsOnMe}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              🔄 重新整理
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

// 根據支持人數生成激勵訊息
function getMotivationalMessage(supportCount) {
  const messages = {
    1: "有一位同學相信你的實力，不要讓他失望！",
    2: "兩位同學都看好你，證明你很有潛力！",
    3: "三位同學支持你，你一定很受歡迎！",
    4: "四位同學對你有信心，你是班上的明星！",
    5: "五位同學支持你，你真的很厲害！"
  };
  
  if (supportCount >= 6) {
    return `哇！有${supportCount}位同學支持你，你是班上的超級明星！大家都相信你會有出色的表現！`;
  }
  
  return messages[supportCount] || "加油！努力讓更多同學看到你的實力！";
}
