import { useState } from 'react';
import { studentsAPI } from '../utils/api';
import { showExportDialog, formatStudentsData } from '../utils/exportUtils';

export default function StudentManager({ students, onStudentsUpdate }) {
  const [isEditing, setIsEditing] = useState(false);
  const [editingStudent, setEditingStudent] = useState(null);
  const [newStudent, setNewStudent] = useState({ seatNumber: '', name: '' });
  const [csvData, setCsvData] = useState('');
  const [showCsvUpload, setShowCsvUpload] = useState(false);

  const handleAddStudent = async () => {
    if (!newStudent.seatNumber || !newStudent.name) {
      alert('請填寫座號和姓名');
      return;
    }

    try {
      await studentsAPI.create(newStudent.name, parseInt(newStudent.seatNumber));
      setNewStudent({ seatNumber: '', name: '' });
      onStudentsUpdate();
      alert('學生新增成功');
    } catch (error) {
      alert(error.response?.data?.error || '新增失敗');
    }
  };

  const handleEditStudent = async (student) => {
    setEditingStudent({ ...student });
    setIsEditing(true);
  };

  const handleUpdateStudent = async () => {
    try {
      await studentsAPI.update(editingStudent.id, editingStudent.name, editingStudent.seat_number);
      setIsEditing(false);
      setEditingStudent(null);
      onStudentsUpdate();
      alert('學生資料更新成功');
    } catch (error) {
      alert(error.response?.data?.error || '更新失敗');
    }
  };

  const handleDeleteStudent = async (studentId) => {
    if (!confirm('確定要刪除這位學生嗎？')) return;

    try {
      await studentsAPI.delete(studentId);
      onStudentsUpdate();
      alert('學生刪除成功');
    } catch (error) {
      alert(error.response?.data?.error || '刪除失敗');
    }
  };

  const handleCsvUpload = async () => {
    if (!csvData.trim()) {
      alert('請輸入CSV資料');
      return;
    }

    try {
      const lines = csvData.trim().split('\n');
      const studentsData = lines.map(line => {
        const [seatNumber, name] = line.split(',').map(s => s.trim());
        return { seatNumber: parseInt(seatNumber), name };
      });

      await studentsAPI.batchCreate(studentsData);
      setCsvData('');
      setShowCsvUpload(false);
      onStudentsUpdate();
      alert('批量上傳成功');
    } catch (error) {
      alert(error.response?.data?.error || '上傳失敗');
    }
  };

  const handleExport = () => {
    const headers = ['座號', '姓名', '建立日期'];
    showExportDialog(students, 'students', headers, formatStudentsData);
  };

  return (
    <div className="p-4 bg-white dark:bg-gray-800 rounded-xl shadow">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-gray-800 dark:text-white">學生資料管理</h2>
        <div className="flex gap-2">
          <button
            onClick={() => setShowCsvUpload(!showCsvUpload)}
            className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
          >
            CSV上傳
          </button>
          <button
            onClick={handleExport}
            className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
          >
            匯出檔案
          </button>
        </div>
      </div>

      {/* CSV上傳區 */}
      {showCsvUpload && (
        <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded">
          <h3 className="font-semibold mb-2 text-gray-700 dark:text-gray-300">CSV批量上傳</h3>
          <p className="text-sm text-gray-500 mb-2">格式：座號,姓名（每行一位學生）</p>
          <textarea
            value={csvData}
            onChange={(e) => setCsvData(e.target.value)}
            placeholder="1,王小明&#10;2,李小華&#10;3,張小美"
            className="w-full h-24 p-2 border rounded dark:bg-gray-600 dark:text-white"
          />
          <div className="flex gap-2 mt-2">
            <button
              onClick={handleCsvUpload}
              className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
            >
              上傳
            </button>
            <button
              onClick={() => setShowCsvUpload(false)}
              className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
            >
              取消
            </button>
          </div>
        </div>
      )}

      {/* 新增學生 */}
      <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded">
        <h3 className="font-semibold mb-2 text-gray-700 dark:text-gray-300">新增學生</h3>
        <div className="flex gap-2">
          <input
            type="number"
            placeholder="座號"
            value={newStudent.seatNumber}
            onChange={(e) => setNewStudent({...newStudent, seatNumber: e.target.value})}
            className="w-20 p-2 border rounded dark:bg-gray-600 dark:text-white"
          />
          <input
            type="text"
            placeholder="姓名"
            value={newStudent.name}
            onChange={(e) => setNewStudent({...newStudent, name: e.target.value})}
            className="flex-1 p-2 border rounded dark:bg-gray-600 dark:text-white"
          />
          <button
            onClick={handleAddStudent}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            新增
          </button>
        </div>
      </div>

      {/* 學生列表 */}
      <div className="max-h-64 overflow-y-auto">
        <table className="w-full text-sm">
          <thead>
            <tr className="text-gray-600 dark:text-gray-300 border-b">
              <th className="text-left py-2">座號</th>
              <th className="text-left py-2">姓名</th>
              <th className="text-right py-2">操作</th>
            </tr>
          </thead>
          <tbody>
            {students.map(student => (
              <tr key={student.id} className="border-b dark:border-gray-600">
                <td className="py-2 font-mono">{student.seat_number}</td>
                <td className="py-2">{student.name}</td>
                <td className="py-2 text-right">
                  <button
                    onClick={() => handleEditStudent(student)}
                    className="px-2 py-1 bg-yellow-500 text-white rounded text-xs mr-1 hover:bg-yellow-600"
                  >
                    編輯
                  </button>
                  <button
                    onClick={() => handleDeleteStudent(student.id)}
                    className="px-2 py-1 bg-red-500 text-white rounded text-xs hover:bg-red-600"
                  >
                    刪除
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 編輯對話框 */}
      {isEditing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg w-96">
            <h3 className="text-lg font-bold mb-4 text-gray-800 dark:text-white">編輯學生</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">座號</label>
                <input
                  type="number"
                  value={editingStudent.seat_number}
                  onChange={(e) => setEditingStudent({...editingStudent, seat_number: parseInt(e.target.value)})}
                  className="w-full p-2 border rounded dark:bg-gray-600 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">姓名</label>
                <input
                  type="text"
                  value={editingStudent.name}
                  onChange={(e) => setEditingStudent({...editingStudent, name: e.target.value})}
                  className="w-full p-2 border rounded dark:bg-gray-600 dark:text-white"
                />
              </div>
            </div>
            <div className="flex gap-2 mt-4">
              <button
                onClick={handleUpdateStudent}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                更新
              </button>
              <button
                onClick={() => setIsEditing(false)}
                className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
