import express from 'express';

const router = express.Router();

// 獲取本週排行榜
router.get('/', async (req, res) => {
  try {
    const currentWeek = getCurrentWeek();

    const scores = await req.db.query(`
      SELECT
        s.seat_number,
        st.name,
        st.seat_number as student_seat_number,
        s.score,
        s.week
      FROM scores s
      JOIN students st ON s.seat_number = st.seat_number
      WHERE s.week = ?
      ORDER BY s.score DESC, st.seat_number ASC
    `, [currentWeek]);

    res.json(scores);
  } catch (error) {
    console.error('獲取分數失敗:', error);
    res.status(500).json({ error: '獲取分數失敗' });
  }
});

// 獲取特定週次的排行榜
router.get('/week/:week', async (req, res) => {
  try {
    const { week } = req.params;

    const scores = await req.db.query(`
      SELECT
        s.seat_number,
        st.name,
        st.seat_number as student_seat_number,
        s.score,
        s.week
      FROM scores s
      JOIN students st ON s.seat_number = st.seat_number
      WHERE s.week = ?
      ORDER BY s.score DESC, st.seat_number ASC
    `, [week]);

    res.json(scores);
  } catch (error) {
    console.error('獲取分數失敗:', error);
    res.status(500).json({ error: '獲取分數失敗' });
  }
});

// 更新學生分數（依座號）
router.put('/seat/:seatNumber', async (req, res) => {
  try {
    const { seatNumber } = req.params;
    const { score, week = getCurrentWeek() } = req.body;

    if (typeof score !== 'number' || score < 0 || score > 100) {
      return res.status(400).json({ error: '分數必須在0-100之間' });
    }

    // 檢查學生是否存在
    const student = await req.db.query('SELECT * FROM students WHERE seat_number = ?', [seatNumber]);
    if (student.length === 0) {
      return res.status(404).json({ error: '學生不存在' });
    }

    // 更新或插入分數
    await req.db.run(`
      INSERT INTO scores (seat_number, score, week, updated_at)
      VALUES (?, ?, ?, CURRENT_TIMESTAMP)
      ON CONFLICT(seat_number, week) DO UPDATE SET
        score = excluded.score,
        updated_at = CURRENT_TIMESTAMP
    `, [seatNumber, score, week]);

    res.json({ message: '分數更新成功', seatNumber, score, week });
  } catch (error) {
    console.error('更新分數失敗:', error);
    res.status(500).json({ error: '更新分數失敗' });
  }
});

// 批量更新分數
router.put('/', async (req, res) => {
  try {
    const { scores, week = getCurrentWeek() } = req.body;

    if (!scores || !Array.isArray(scores)) {
      return res.status(400).json({ error: '分數資料格式錯誤' });
    }

    for (const scoreData of scores) {
      const { seatNumber, score } = scoreData;

      if (typeof score !== 'number' || score < 0 || score > 100) {
        return res.status(400).json({ error: `座號${seatNumber}學生的分數必須在0-100之間` });
      }

      await req.db.run(`
        INSERT INTO scores (seat_number, score, week, updated_at)
        VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        ON CONFLICT(seat_number, week) DO UPDATE SET
          score = excluded.score,
          updated_at = CURRENT_TIMESTAMP
      `, [seatNumber, score, week]);
    }

    res.json({ message: '批量更新分數成功', count: scores.length, week });
  } catch (error) {
    console.error('批量更新分數失敗:', error);
    res.status(500).json({ error: '批量更新分數失敗' });
  }
});

// 隨機更新所有學生分數（用於測試）
router.post('/random', async (req, res) => {
  try {
    const currentWeek = getCurrentWeek();

    const students = await req.db.query('SELECT seat_number FROM students');

    for (const student of students) {
      const randomScore = Math.floor(Math.random() * 101); // 0-100
      await req.db.run(`
        INSERT INTO scores (seat_number, score, week, updated_at)
        VALUES (?, ?, ?, CURRENT_TIMESTAMP)
        ON CONFLICT(seat_number, week) DO UPDATE SET
          score = excluded.score,
          updated_at = CURRENT_TIMESTAMP
      `, [student.seat_number, randomScore, currentWeek]);
    }

    res.json({ message: '隨機分數生成成功', week: currentWeek });
  } catch (error) {
    console.error('生成隨機分數失敗:', error);
    res.status(500).json({ error: '生成隨機分數失敗' });
  }
});

function getCurrentWeek() {
  const now = new Date();
  const startOfYear = new Date(now.getFullYear(), 0, 1);
  const pastDaysOfYear = (now - startOfYear) / 86400000;
  return Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
}

export default router;
