import React from 'react';

export default function StudentSelector({ students, selectedStudent, onStudentSelect }) {
  return (
    <div className="pastel-card" style={{marginBottom: '2rem'}}>
      <div className="flex items-center gap-3 mb-6">
        <span className="text-3xl">🎯</span>
        <h2 style={{fontFamily: 'Architects Daughter, cursive', color: '#2d2d2d'}} className="text-2xl font-bold">
          選擇你的座號
        </h2>
      </div>
      <div className="grid grid-cols-7 gap-3">
        {students.map(student => (
          <button
            key={student.seat_number}
            onClick={() => onStudentSelect(student)}
            className={`
              p-4 rounded-3xl border-2 transition-all duration-300 text-sm font-medium transform hover:scale-105
              ${selectedStudent?.seat_number === student.seat_number
                ? 'shadow-lg scale-105'
                : 'hover:shadow-md'
              }
            `}
            style={{
              background: selectedStudent?.seat_number === student.seat_number
                ? 'linear-gradient(135deg, #ffb6d5 0%, #ff9ec7 100%)'
                : 'linear-gradient(135deg, #ffffff 0%, #fef9fc 100%)',
              color: selectedStudent?.seat_number === student.seat_number ? '#ffffff' : '#2d2d2d',
              borderColor: selectedStudent?.seat_number === student.seat_number ? '#ffb6d5' : '#f0f0f0',
              fontFamily: 'Poppins, sans-serif'
            }}
          >
            <div className="text-xs opacity-75 mb-1">座號</div>
            <div className="font-bold text-lg">{student.seat_number}</div>
            <div className="text-xs mt-1 truncate">{student.name}</div>
          </button>
        ))}
      </div>
      {selectedStudent && (
        <div className="mt-6 p-4 rounded-2xl" style={{background: '#aeeaff', border: '2px solid #87ceeb'}}>
          <div className="flex items-center gap-2">
            <span className="text-xl">✨</span>
            <p style={{color: '#2d2d2d', fontFamily: 'Poppins, sans-serif', fontWeight: '500'}}>
              已選擇：座號 {selectedStudent.seat_number} - {selectedStudent.name}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
