import React, { useState, useEffect } from 'react';
import { betsAPI } from '../utils/api';

export default function Leaderboard({ scores }) {
  const [investorsData, setInvestorsData] = useState({});

  useEffect(() => {
    fetchInvestorsData();
  }, [scores]);

  const fetchInvestorsData = async () => {
    try {
      const investorsMap = {};

      // 為每位學生獲取投資者資料
      for (const score of scores) {
        try {
          const response = await betsAPI.getBetsOnStudent(score.seat_number);
          investorsMap[score.seat_number] = response.data;
        } catch (error) {
          investorsMap[score.seat_number] = [];
        }
      }

      setInvestorsData(investorsMap);
    } catch (error) {
      console.error('獲取投資者資料失敗:', error);
    }
  };
  return (
    <div className="pastel-card">
      <div className="flex items-center gap-3 mb-6">
        <span className="text-3xl">🏆</span>
        <h2 style={{fontFamily: 'Architects Daughter, cursive', color: '#2d2d2d'}} className="text-2xl font-bold">
          本週排行榜
        </h2>
      </div>
      <div className="overflow-x-auto">
        <table className="pastel-table">
          <thead>
            <tr>
              <th style={{fontFamily: 'Poppins, sans-serif'}}>#</th>
              <th style={{fontFamily: 'Poppins, sans-serif'}}>座號</th>
              <th style={{fontFamily: 'Poppins, sans-serif'}}>學生</th>
              <th style={{fontFamily: 'Poppins, sans-serif'}}>投資者</th>
              <th style={{fontFamily: 'Poppins, sans-serif', textAlign: 'right'}}>分數</th>
            </tr>
          </thead>
          <tbody>
            {scores.map((s, i) => {
              const investors = investorsData[s.seat_number] || [];
              const rankEmoji = i === 0 ? '🥇' : i === 1 ? '🥈' : i === 2 ? '🥉' : '';
              return (
                <tr key={s.seat_number}>
                  <td style={{fontFamily: 'Poppins, sans-serif', fontWeight: '600'}}>
                    <div className="flex items-center gap-2">
                      {rankEmoji && <span className="text-lg">{rankEmoji}</span>}
                      {i + 1}
                    </div>
                  </td>
                  <td style={{fontFamily: 'Poppins, sans-serif', fontWeight: '700', color: '#ffb6d5'}}>{s.seat_number}</td>
                  <td style={{fontFamily: 'Poppins, sans-serif', fontWeight: '500'}}>{s.name}</td>
                  <td>
                    {investors.length > 0 ? (
                      <div className="flex flex-wrap gap-1">
                        {investors.map((investor, idx) => (
                          <span
                            key={idx}
                            className="pastel-tag pastel-tag-green"
                            style={{fontSize: '0.8rem'}}
                          >
                            {investor.bettor_seat_number}
                          </span>
                        ))}
                      </div>
                    ) : (
                      <span style={{color: '#999999', fontSize: '0.9rem', fontFamily: 'Poppins, sans-serif'}}>無投資者</span>
                    )}
                  </td>
                  <td style={{textAlign: 'right', fontFamily: 'Poppins, sans-serif', fontWeight: '700', fontSize: '1.1rem', color: '#2d2d2d'}}>
                    {s.score}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
}
