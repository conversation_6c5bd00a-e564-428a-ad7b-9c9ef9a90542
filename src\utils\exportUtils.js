// 檔案匯出工具

// 修復CSV編碼問題，支援繁體中文
export const exportToCSV = (data, filename, headers = null) => {
  let csvContent = '';
  
  // 添加BOM以支援繁體中文
  const BOM = '\uFEFF';
  
  // 如果有標題行
  if (headers) {
    csvContent += headers.join(',') + '\n';
  }
  
  // 添加數據行
  csvContent += data.map(row => {
    return Array.isArray(row) 
      ? row.map(field => `"${field}"`).join(',')
      : Object.values(row).map(field => `"${field}"`).join(',');
  }).join('\n');
  
  // 創建Blob並下載
  const blob = new Blob([BOM + csvContent], { 
    type: 'text/csv;charset=utf-8;' 
  });
  downloadFile(blob, filename);
};

// 匯出Excel格式（使用CSV格式但副檔名為.xlsx）
export const exportToExcel = (data, filename, headers = null) => {
  // 創建工作表內容
  let content = '';
  
  if (headers) {
    content += headers.join('\t') + '\n';
  }
  
  content += data.map(row => {
    return Array.isArray(row) 
      ? row.join('\t')
      : Object.values(row).join('\t');
  }).join('\n');
  
  // 使用製表符分隔的格式，Excel可以正確識別
  const blob = new Blob(['\uFEFF' + content], { 
    type: 'application/vnd.ms-excel;charset=utf-8;' 
  });
  downloadFile(blob, filename.replace('.csv', '.xls'));
};

// 匯出TXT格式
export const exportToTXT = (data, filename, headers = null) => {
  let content = '';
  
  if (headers) {
    content += headers.join('\t') + '\n';
    content += '='.repeat(50) + '\n';
  }
  
  content += data.map(row => {
    return Array.isArray(row) 
      ? row.join('\t')
      : Object.values(row).join('\t');
  }).join('\n');
  
  const blob = new Blob(['\uFEFF' + content], { 
    type: 'text/plain;charset=utf-8;' 
  });
  downloadFile(blob, filename.replace('.csv', '.txt'));
};

// 通用下載函數
const downloadFile = (blob, filename) => {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

// 格式化學生資料
export const formatStudentsData = (students) => {
  return students.map(student => [
    student.seat_number,
    student.name,
    new Date(student.created_at).toLocaleDateString('zh-TW')
  ]);
};

// 格式化分數資料
export const formatScoresData = (scores) => {
  return scores.map((score, index) => [
    index + 1, // 排名
    score.seat_number,
    score.name,
    score.score
  ]);
};

// 格式化結算資料
export const formatSettlementData = (settlementResults) => {
  return Object.values(settlementResults)
    .filter(result => result.totalBets > 0)
    .sort((a, b) => b.totalWinnings - a.totalWinnings)
    .map(result => {
      const successRate = result.totalBets > 0 
        ? Math.round((result.betsPlaced.filter(b => b.isWin).length / result.totalBets) * 100)
        : 0;
      
      return [
        result.seatNumber,
        result.name,
        result.totalBets,
        result.totalWinnings,
        `${successRate}%`
      ];
    });
};

// 多格式匯出選擇器
export const showExportDialog = (data, baseFilename, headers, formatFunction = null) => {
  const formattedData = formatFunction ? formatFunction(data) : data;
  
  const formats = [
    { name: 'CSV', extension: '.csv', handler: exportToCSV },
    { name: 'Excel', extension: '.xls', handler: exportToExcel },
    { name: 'TXT', extension: '.txt', handler: exportToTXT }
  ];
  
  // 創建選擇對話框
  const dialog = document.createElement('div');
  dialog.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
  dialog.innerHTML = `
    <div class="bg-white dark:bg-gray-800 p-6 rounded-lg w-80">
      <h3 class="text-lg font-bold mb-4 text-gray-800 dark:text-white">選擇匯出格式</h3>
      <div class="space-y-2">
        ${formats.map(format => `
          <button 
            class="w-full p-3 text-left bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded transition-colors"
            onclick="exportFormat('${format.name}')"
          >
            <div class="font-medium text-gray-800 dark:text-white">${format.name} 格式</div>
            <div class="text-sm text-gray-500 dark:text-gray-400">${baseFilename}${format.extension}</div>
          </button>
        `).join('')}
      </div>
      <button 
        onclick="closeDialog()"
        class="mt-4 w-full px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
      >
        取消
      </button>
    </div>
  `;
  
  // 添加事件處理
  window.exportFormat = (formatName) => {
    const format = formats.find(f => f.name === formatName);
    if (format) {
      format.handler(formattedData, baseFilename + format.extension, headers);
    }
    document.body.removeChild(dialog);
    delete window.exportFormat;
    delete window.closeDialog;
  };
  
  window.closeDialog = () => {
    document.body.removeChild(dialog);
    delete window.exportFormat;
    delete window.closeDialog;
  };
  
  document.body.appendChild(dialog);
};
