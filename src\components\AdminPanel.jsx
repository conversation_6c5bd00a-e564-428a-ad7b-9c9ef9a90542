import { useState } from 'react';
import StudentManager from './StudentManager';
import ScoreManager from './ScoreManager';
import SettlementSystem from './SettlementSystem';

export default function AdminPanel({ students, scores, onStudentsUpdate, onScoresUpdate }) {
  const [activeTab, setActiveTab] = useState('students');

  const tabs = [
    { id: 'students', name: '學生管理', icon: '👥' },
    { id: 'scores', name: '排行榜管理', icon: '🏆' },
    { id: 'settlement', name: '結算系統', icon: '💰' }
  ];

  return (
    <div className="pastel-card">
      <div className="flex justify-between items-center mb-8">
        <div className="flex items-center gap-4">
          <span className="text-4xl">⚙️</span>
          <div>
            <h1 style={{fontFamily: 'Architects Daughter, cursive', color: '#2d2d2d'}} className="text-3xl font-bold">
              管理員面板
            </h1>
            <p style={{fontFamily: 'Poppins, sans-serif', color: '#666666', fontSize: '1rem'}}>
              系統管理與數據維護 📊
            </p>
          </div>
        </div>
      </div>

      {/* 標籤導航 */}
      <div className="flex space-x-2 mb-8 p-2 rounded-3xl" style={{background: '#fff7fa'}}>
        {tabs.map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`
              flex-1 flex items-center justify-center gap-3 py-3 px-6 rounded-2xl transition-all duration-300 font-medium
              ${activeTab === tab.id ? 'transform scale-105' : 'hover:scale-102'}
            `}
            style={{
              background: activeTab === tab.id
                ? 'linear-gradient(135deg, #ffb6d5 0%, #ff9ec7 100%)'
                : 'transparent',
              color: activeTab === tab.id ? '#ffffff' : '#666666',
              fontFamily: 'Poppins, sans-serif',
              boxShadow: activeTab === tab.id ? '0 4px 16px rgba(255, 182, 213, 0.3)' : 'none'
            }}
          >
            <span className="text-xl">{tab.icon}</span>
            <span>{tab.name}</span>
          </button>
        ))}
      </div>

      {/* 標籤內容 */}
      <div className="min-h-96">
        {activeTab === 'students' && (
          <StudentManager 
            students={students} 
            onStudentsUpdate={onStudentsUpdate}
          />
        )}
        
        {activeTab === 'scores' && (
          <ScoreManager 
            scores={scores} 
            onScoresUpdate={onScoresUpdate}
          />
        )}
        
        {activeTab === 'settlement' && (
          <SettlementSystem 
            students={students}
            scores={scores}
          />
        )}
      </div>
    </div>
  );
}
