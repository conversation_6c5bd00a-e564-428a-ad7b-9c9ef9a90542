import { useState } from 'react';
import { scoresAPI } from '../utils/api';
import { showExportDialog, formatScoresData } from '../utils/exportUtils';

export default function ScoreManager({ scores, onScoresUpdate }) {
  const [isEditing, setIsEditing] = useState(false);
  const [editingScore, setEditingScore] = useState(null);
  const [csvData, setCsvData] = useState('');
  const [showCsvUpload, setShowCsvUpload] = useState(false);

  const handleEditScore = (score) => {
    setEditingScore({ ...score });
    setIsEditing(true);
  };

  const handleUpdateScore = async () => {
    try {
      await scoresAPI.updateStudentBySeat(
        editingScore.seat_number, 
        editingScore.score
      );
      setIsEditing(false);
      setEditingScore(null);
      onScoresUpdate();
      alert('分數更新成功');
    } catch (error) {
      alert(error.response?.data?.error || '更新失敗');
    }
  };

  const handleCsvUpload = async () => {
    if (!csvData.trim()) {
      alert('請輸入CSV資料');
      return;
    }

    try {
      const lines = csvData.trim().split('\n');
      const scoresData = lines.map(line => {
        const [seatNumber, score] = line.split(',').map(s => s.trim());
        return { seatNumber: parseInt(seatNumber), score: parseInt(score) };
      });

      await scoresAPI.updateBatch(scoresData);
      setCsvData('');
      setShowCsvUpload(false);
      onScoresUpdate();
      alert('批量更新成功');
    } catch (error) {
      alert(error.response?.data?.error || '更新失敗');
    }
  };

  const handleExport = () => {
    const headers = ['排名', '座號', '姓名', '分數'];
    showExportDialog(scores, 'scores', headers, formatScoresData);
  };

  const generateRandomScores = async () => {
    if (!confirm('確定要生成隨機分數嗎？這會覆蓋現有分數。')) return;

    try {
      await scoresAPI.generateRandom();
      onScoresUpdate();
      alert('隨機分數生成成功');
    } catch (error) {
      alert(error.response?.data?.error || '生成失敗');
    }
  };

  return (
    <div className="p-4 bg-white dark:bg-gray-800 rounded-xl shadow">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-bold text-gray-800 dark:text-white">排行榜管理</h2>
        <div className="flex gap-2">
          <button
            onClick={() => setShowCsvUpload(!showCsvUpload)}
            className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
          >
            CSV上傳
          </button>
          <button
            onClick={handleExport}
            className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
          >
            匯出檔案
          </button>
          <button
            onClick={generateRandomScores}
            className="px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700"
          >
            隨機分數
          </button>
        </div>
      </div>

      {/* CSV上傳區 */}
      {showCsvUpload && (
        <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded">
          <h3 className="font-semibold mb-2 text-gray-700 dark:text-gray-300">CSV批量上傳</h3>
          <p className="text-sm text-gray-500 mb-2">格式：座號,分數（每行一位學生）</p>
          <textarea
            value={csvData}
            onChange={(e) => setCsvData(e.target.value)}
            placeholder="1,85&#10;2,92&#10;3,78"
            className="w-full h-24 p-2 border rounded dark:bg-gray-600 dark:text-white"
          />
          <div className="flex gap-2 mt-2">
            <button
              onClick={handleCsvUpload}
              className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
            >
              上傳
            </button>
            <button
              onClick={() => setShowCsvUpload(false)}
              className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
            >
              取消
            </button>
          </div>
        </div>
      )}

      {/* 分數列表 */}
      <div className="max-h-64 overflow-y-auto">
        <table className="w-full text-sm">
          <thead>
            <tr className="text-gray-600 dark:text-gray-300 border-b">
              <th className="text-left py-2">排名</th>
              <th className="text-left py-2">座號</th>
              <th className="text-left py-2">姓名</th>
              <th className="text-left py-2">分數</th>
              <th className="text-right py-2">操作</th>
            </tr>
          </thead>
          <tbody>
            {scores.map((score, index) => (
              <tr key={score.seat_number} className="border-b dark:border-gray-600">
                <td className="py-2 font-bold text-blue-600 dark:text-blue-400">#{index + 1}</td>
                <td className="py-2 font-mono">{score.seat_number}</td>
                <td className="py-2">{score.name}</td>
                <td className="py-2 font-semibold">{score.score}</td>
                <td className="py-2 text-right">
                  <button
                    onClick={() => handleEditScore(score)}
                    className="px-2 py-1 bg-yellow-500 text-white rounded text-xs hover:bg-yellow-600"
                  >
                    編輯
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 編輯對話框 */}
      {isEditing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg w-96">
            <h3 className="text-lg font-bold mb-4 text-gray-800 dark:text-white">編輯分數</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  學生：座號 {editingScore.seat_number} - {editingScore.name}
                </label>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">分數</label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={editingScore.score}
                  onChange={(e) => setEditingScore({...editingScore, score: parseInt(e.target.value)})}
                  className="w-full p-2 border rounded dark:bg-gray-600 dark:text-white"
                />
              </div>
            </div>
            <div className="flex gap-2 mt-4">
              <button
                onClick={handleUpdateScore}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                更新
              </button>
              <button
                onClick={() => setIsEditing(false)}
                className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
